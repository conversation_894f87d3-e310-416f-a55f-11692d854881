from fastapi import APIRouter, Depends, Request
from sqlalchemy.ext.asyncio import AsyncSession
from config.get_db import get_db
from module_admin.annotation.log_annotation import Log
from module_admin.entity.vo.student_vo import StudentModel, StudentPageQueryModel, DeleteStudentModel, StudentPageResponseModel
from module_admin.service.login_service import LoginService
from module_admin.service.student_service import StudentService
from utils.response_util import ResponseUtil
from utils.log_util import logger


from module_admin.entity.vo.user_vo import CurrentUserModel
from config.enums import BusinessType


studentController = APIRouter(prefix='/system/student', tags=['学生管理'])


@studentController.get('/list', response_model=StudentPageResponseModel, dependencies=[Depends(LoginService.get_current_user)])
async def get_system_student_list(
    request: Request,
    student_query: StudentPageQueryModel = Depends(StudentPageQueryModel),
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    # 获取分页数据
    student_page_query_result = await StudentService.get_student_list_services(
        query_db, student_query, is_page=True
    )
    logger.info('获取成功')

    return ResponseUtil.success(model_content=student_page_query_result)


@studentController.post('', dependencies=[Depends(LoginService.get_current_user)])
@Log(title='学生管理', business_type=BusinessType.INSERT)
async def add_system_student(
    request: Request,
    add_student: StudentModel,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    add_student_result = await StudentService.add_student_services(query_db, add_student)
    logger.info(add_student_result.message)

    return ResponseUtil.success(msg=add_student_result.message)


@studentController.put('', dependencies=[Depends(LoginService.get_current_user)])
@Log(title='学生管理', business_type=BusinessType.UPDATE)
async def edit_system_student(
    request: Request,
    edit_student: StudentModel,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    edit_student_result = await StudentService.edit_student_services(query_db, edit_student)
    logger.info(edit_student_result.message)

    return ResponseUtil.success(msg=edit_student_result.message)


@studentController.delete('/{student_ids}', dependencies=[Depends(LoginService.get_current_user)])
@Log(title='学生管理', business_type=BusinessType.DELETE)
async def delete_system_student(
    request: Request,
    student_ids: str,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    delete_student = DeleteStudentModel(student_ids=student_ids)
    delete_student_result = await StudentService.delete_student_services(query_db, delete_student)
    logger.info(delete_student_result.message)

    return ResponseUtil.success(msg=delete_student_result.message)


@studentController.get(
    '/{student_id}', 
    response_model=StudentModel, 
    dependencies=[Depends(LoginService.get_current_user)]
)
async def query_detail_system_student(
    request: Request,
    student_id: str,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    detail_student_result = await StudentService.student_detail_services(query_db, student_id)
    logger.info(f'获取student_id为{student_id}的信息成功')

    return ResponseUtil.success(data=detail_student_result)


@studentController.get('/checkStudentNumberUnique', dependencies=[Depends(LoginService.get_current_user)])
async def check_system_student_number_unique(
    request: Request,
    student_query: StudentModel = Depends(StudentModel),
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    unique_result = await StudentService.check_student_number_unique_services(query_db, student_query)
    logger.info('校验成功')

    return ResponseUtil.success(data=unique_result)
