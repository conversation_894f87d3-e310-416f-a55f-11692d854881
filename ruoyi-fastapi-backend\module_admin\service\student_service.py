import uuid
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from module_admin.dao.student_dao import StudentDao
from module_admin.entity.vo.student_vo import StudentModel, StudentPageQueryModel, DeleteStudentModel
from module_admin.entity.vo.common_vo import CrudResponseModel

from utils.common_util import CamelCaseUtil
from exceptions.exception import ServiceException


class StudentService:
    """
    学生管理模块服务层
    """

    @classmethod
    async def get_student_list_services(cls, query_db: AsyncSession, query_object: StudentPageQueryModel, is_page: bool = False):
        """
        获取学生列表信息service

        :param query_db: orm对象
        :param query_object: 查询参数对象
        :param is_page: 是否开启分页
        :return: 学生列表信息对象
        """
        student_list_result = await StudentDao.get_student_list(query_db, query_object, is_page)

        return student_list_result

    @classmethod
    async def check_student_number_unique_services(cls, query_db: AsyncSession, page_object: StudentModel):
        """
        校验学号是否唯一service

        :param query_db: orm对象
        :param page_object: 学生对象
        :return: 校验结果
        """
        student_id = page_object.id if page_object.id else -1
        student = await StudentDao.get_student_detail_by_info(query_db, page_object)
        if student and student.id != student_id:
            return '1'
        return '0'

    @classmethod
    async def add_student_services(cls, query_db: AsyncSession, page_object: StudentModel):
        """
        新增学生信息service

        :param query_db: orm对象
        :param page_object: 新增学生对象
        :return: 新增学生校验结果
        """
        if not page_object.id:
            page_object.id = str(uuid.uuid4())
        page_object.create_time = datetime.now()
        page_object.update_time = datetime.now()
        try:
            await StudentDao.add_student_dao(query_db, page_object)
            await query_db.commit()
            return CrudResponseModel(is_success=True, message='新增成功')
        except Exception as e:
            await query_db.rollback()
            raise e

    @classmethod
    async def edit_student_services(cls, query_db: AsyncSession, page_object: StudentModel):
        """
        编辑学生信息service

        :param query_db: orm对象
        :param page_object: 编辑学生对象
        :return: 编辑学生校验结果
        """
        page_object.update_time = datetime.now()
        try:
            await StudentDao.edit_student_dao(query_db, page_object)
            await query_db.commit()
            return CrudResponseModel(is_success=True, message='更新成功')
        except Exception as e:
            await query_db.rollback()
            raise e

    @classmethod
    async def delete_student_services(cls, query_db: AsyncSession, page_object: DeleteStudentModel):
        """
        删除学生信息service

        :param query_db: orm对象
        :param page_object: 删除学生对象
        :return: 删除学生校验结果
        """
        if page_object.student_ids:
            student_id_list = page_object.student_ids.split(',')
            try:
                for student_id in student_id_list:
                    await StudentDao.delete_student_dao(query_db, StudentModel(id=student_id, update_time=datetime.now()))
                await query_db.commit()
                return CrudResponseModel(is_success=True, message='删除成功')
            except Exception as e:
                await query_db.rollback()
                raise e
        else:
            raise ServiceException(message='传入学生id为空')

    @classmethod
    async def student_detail_services(cls, query_db: AsyncSession, student_id: str):
        """
        获取学生详细信息service

        :param query_db: orm对象
        :param student_id: 学生id
        :return: 学生id对应的信息
        """
        student = await StudentDao.get_student_detail_by_id(query_db, student_id=student_id)
        if student:
            result = StudentModel(**CamelCaseUtil.transform_result(student))
        else:
            result = StudentModel(**dict())

        return result
