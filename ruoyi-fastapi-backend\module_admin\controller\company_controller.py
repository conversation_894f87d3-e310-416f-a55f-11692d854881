from datetime import datetime
from fastapi import APIRouter, Depends, Request
from pydantic_validation_decorator import <PERSON>idateFields
from sqlalchemy.ext.asyncio import AsyncSession

from config.enums import BusinessType
from config.get_db import get_db
from module_admin.annotation.log_annotation import Log
from module_admin.entity.vo.company_vo import (
    DeleteCompanyModel, 
    CompanyModel, 
    CompanyPageQueryModel, 
    CompanyPageResponseModel
)
from module_admin.entity.vo.user_vo import CurrentUserModel
from module_admin.service.company_service import CompanyService
from module_admin.service.login_service import LoginService
from utils.log_util import logger
from utils.response_util import ResponseUtil


companyController = APIRouter(prefix='/system/company', dependencies=[Depends(LoginService.get_current_user)])


@companyController.get(
    '/list', 
    response_model=CompanyPageResponseModel, 
    dependencies=[Depends(LoginService.get_current_user)]
)
async def get_system_company_list(
    request: Request,
    company_page_query: CompanyPageQueryModel = Depends(CompanyPageQueryModel.as_query),
    query_db: AsyncSession = Depends(get_db),
):
    # 获取分页数据
    company_page_query_result = await CompanyService.get_company_list_services(
        query_db, company_page_query, is_page=True
    )
    logger.info('获取成功')

    return ResponseUtil.success(data=company_page_query_result)


@companyController.post('', dependencies=[Depends(LoginService.get_current_user)])
@ValidateFields(validate_model='add_company')
@Log(title='公司管理', business_type=BusinessType.INSERT)
async def add_system_company(
    request: Request,
    add_company: CompanyModel,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    add_company.create_time = datetime.now()
    add_company.update_time = datetime.now()
    add_company_result = await CompanyService.add_company_services(query_db, add_company)
    logger.info(add_company_result.message)

    return ResponseUtil.success(data=add_company_result)


@companyController.put('', dependencies=[Depends(LoginService.get_current_user)])
@ValidateFields(validate_model='edit_company')
@Log(title='公司管理', business_type=BusinessType.UPDATE)
async def edit_system_company(
    request: Request,
    edit_company: CompanyModel,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    edit_company.update_time = datetime.now()
    edit_company_result = await CompanyService.edit_company_services(query_db, edit_company)
    logger.info(edit_company_result.message)

    return ResponseUtil.success(msg=edit_company_result.message)


@companyController.delete('/{company_ids}', dependencies=[Depends(LoginService.get_current_user)])
@Log(title='公司管理', business_type=BusinessType.DELETE)
async def delete_system_company(
    request: Request,
    company_ids: str,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    delete_company = DeleteCompanyModel(company_ids=company_ids)
    delete_company_result = await CompanyService.delete_company_services(query_db, delete_company)
    logger.info(delete_company_result.message)

    return ResponseUtil.success(msg=delete_company_result.message)


@companyController.get(
    '/{company_id}', 
    response_model=CompanyModel, 
    dependencies=[Depends(LoginService.get_current_user)]
)
async def query_detail_system_company(
    request: Request,
    company_id: str,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    detail_company_result = await CompanyService.company_detail_services(query_db, company_id)
    logger.info(f'获取company_id为{company_id}的信息成功')

    return ResponseUtil.success(data=detail_company_result)
