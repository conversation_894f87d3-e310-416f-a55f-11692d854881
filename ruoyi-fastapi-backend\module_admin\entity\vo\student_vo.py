from datetime import datetime, date
from typing import Optional
from pydantic import BaseModel, Field, ConfigDict
from pydantic.alias_generators import to_camel
from module_admin.annotation.pydantic_annotation import as_query
from utils.page_util import PageResponseModel


class StudentModel(BaseModel):
    """
    学生信息模型
    """

    model_config = ConfigDict(alias_generator=to_camel)

    id: Optional[str] = Field(default=None, description='学生ID')
    name: Optional[str] = Field(default=None, description='学生姓名')
    gender: Optional[str] = Field(default=None, description='性别(M:男,F:女)')
    birth_date: Optional[date] = Field(default=None, description='出生日期')
    student_number: Optional[str] = Field(default=None, description='学号')
    class_id: Optional[str] = Field(default=None, description='班级ID')
    contact_phone: Optional[str] = Field(default=None, description='联系电话')
    home_address: Optional[str] = Field(default=None, description='家庭住址')
    status: Optional[str] = Field(default='1', description='状态(1:在读,2:休学,0:退学)')
    is_delete: Optional[str] = Field(default='0', description='是否删除(1:已删除,0:未删除)')
    create_time: Optional[datetime] = Field(default=None, description='创建时间')
    update_time: Optional[datetime] = Field(default=None, description='更新时间')


@as_query
class StudentPageQueryModel(StudentModel):
    """
    学生信息分页查询模型
    """

    page_num: int = Field(default=1, description='当前页码')
    page_size: int = Field(default=10, description='每页记录数')
    begin_time: Optional[str] = Field(default=None, description='开始时间')
    end_time: Optional[str] = Field(default=None, description='结束时间')


@as_query
class StudentQueryModel(StudentModel):
    """
    学生信息查询模型
    """

    begin_time: Optional[str] = Field(default=None, description='开始时间')
    end_time: Optional[str] = Field(default=None, description='结束时间')


class DeleteStudentModel(BaseModel):
    """
    删除学生模型
    """

    student_ids: str = Field(default=None, description='需要删除的学生id')


class StudentPageResponseModel(PageResponseModel):
    """
    学生管理分页响应模型
    """

    rows: list[StudentModel] = Field(default_factory=list, description='学生列表数据')
