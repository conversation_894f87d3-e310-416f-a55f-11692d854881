from sqlalchemy import Column, String, Date, DateTime
from sqlalchemy.sql import func
from config.database import Base


class Student(Base):
    """
    学生信息表
    """
    __tablename__ = 'student'

    id = Column(String(64), primary_key=True, comment='学生ID')
    name = Column(String(100), nullable=False, comment='学生姓名')
    gender = Column(String(2), comment='性别(M:男,F:女)')
    birth_date = Column(Date, comment='出生日期')
    student_number = Column(String(50), nullable=False, unique=True, comment='学号')
    class_id = Column(String(64), comment='班级ID')
    contact_phone = Column(String(20), comment='联系电话')
    home_address = Column(String(255), comment='家庭住址')
    status = Column(String(2), default='1', comment='状态(1:在读,2:休学,0:退学)')
    is_delete = Column(String(2), default='0', comment='是否删除(1:已删除,0:未删除)')
    create_time = Column(DateTime, default=func.now(), comment='创建时间')
    update_time = Column(DateTime, default=func.now(), onupdate=func.now(), comment='更新时间')
