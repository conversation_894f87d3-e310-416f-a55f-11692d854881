import uuid
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from config.constant import CommonConstant
from exceptions.exception import ServiceException
from module_admin.dao.company_dao import CompanyDao
from module_admin.entity.vo.common_vo import CrudResponseModel
from module_admin.entity.vo.company_vo import DeleteCompanyModel, CompanyModel, CompanyPageQueryModel
from utils.common_util import CamelCaseUtil


class CompanyService:
    """
    公司管理模块服务层
    """

    @classmethod
    async def get_company_list_services(cls, query_db: AsyncSession, query_object: CompanyPageQueryModel, is_page: bool = False):
        """
        获取公司列表信息service

        :param query_db: orm对象
        :param query_object: 查询参数对象
        :param is_page: 是否开启分页
        :return: 公司列表信息对象
        """
        company_list_result = await CompanyDao.get_company_list(query_db, query_object, is_page)

        return company_list_result

    @classmethod
    async def check_company_name_unique_services(cls, query_db: AsyncSession, page_object: CompanyModel):
        """
        校验公司名称是否唯一service

        :param query_db: orm对象
        :param page_object: 公司对象
        :return: 校验结果
        """
        company_id = -1 if page_object.id is None else page_object.id
        company = await CompanyDao.get_company_detail_by_info(
            query_db, CompanyModel(name=page_object.name)
        )
        if company and company.id != company_id:
            return CommonConstant.NOT_UNIQUE
        return CommonConstant.UNIQUE

    @classmethod
    async def add_company_services(cls, query_db: AsyncSession, page_object: CompanyModel):
        """
        新增公司信息service

        :param query_db: orm对象
        :param page_object: 新增公司对象
        :return: 新增公司校验结果
        """
        if not await cls.check_company_name_unique_services(query_db, page_object):
            raise ServiceException(message=f'新增公司{page_object.name}失败，公司名称已存在')
        
        # 生成UUID作为主键
        page_object.id = str(uuid.uuid4())
        
        try:
            await CompanyDao.add_company_dao(query_db, page_object)
            await query_db.commit()
            return CrudResponseModel(is_success=True, message='新增成功')
        except Exception as e:
            await query_db.rollback()
            raise e

    @classmethod
    async def edit_company_services(cls, query_db: AsyncSession, page_object: CompanyModel):
        """
        编辑公司信息service

        :param query_db: orm对象
        :param page_object: 编辑公司对象
        :return: 编辑公司校验结果
        """
        edit_company = page_object.model_dump(exclude_unset=True)
        if page_object.name:
            if not await cls.check_company_name_unique_services(query_db, page_object):
                raise ServiceException(message=f'修改公司{page_object.name}失败，公司名称已存在')
        try:
            await CompanyDao.edit_company_dao(query_db, edit_company)
            await query_db.commit()
            return CrudResponseModel(is_success=True, message='更新成功')
        except Exception as e:
            await query_db.rollback()
            raise e

    @classmethod
    async def delete_company_services(cls, query_db: AsyncSession, page_object: DeleteCompanyModel):
        """
        删除公司信息service

        :param query_db: orm对象
        :param page_object: 删除公司对象
        :return: 删除公司校验结果
        """
        if page_object.company_ids:
            company_id_list = page_object.company_ids.split(',')
            try:
                for company_id in company_id_list:
                    await CompanyDao.delete_company_dao(query_db, CompanyModel(id=company_id, update_time=datetime.now()))
                await query_db.commit()
                return CrudResponseModel(is_success=True, message='删除成功')
            except Exception as e:
                await query_db.rollback()
                raise e
        else:
            raise ServiceException(message='传入公司id为空')

    @classmethod
    async def company_detail_services(cls, query_db: AsyncSession, company_id: str):
        """
        获取公司详细信息service

        :param query_db: orm对象
        :param company_id: 公司id
        :return: 公司id对应的信息
        """
        company = await CompanyDao.get_company_detail_by_id(query_db, company_id=company_id)
        if company:
            result = CompanyModel(**CamelCaseUtil.transform_result(company))
        else:
            result = CompanyModel(**dict())

        return result
