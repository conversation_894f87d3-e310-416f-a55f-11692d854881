from sqlalchemy import and_, or_, select, update, desc
from sqlalchemy.ext.asyncio import AsyncSession
from module_admin.entity.do.student_do import Student
from module_admin.entity.vo.student_vo import StudentModel, StudentQueryModel
from utils.page_util import PageUtil


class StudentDao:
    """
    学生管理模块数据库操作层
    """

    @classmethod
    async def get_student_list(cls, query_db: AsyncSession, query_object: StudentQueryModel, is_page: bool = False):
        """
        根据查询参数获取学生列表信息

        :param query_db: orm对象
        :param query_object: 查询参数对象
        :param is_page: 是否开启分页
        :return: 学生列表信息对象
        """
        query = select(Student).where(Student.is_delete == '0')
        
        if query_object.name:
            query = query.where(Student.name.like(f'%{query_object.name}%'))
        if query_object.gender:
            query = query.where(Student.gender == query_object.gender)
        if query_object.student_number:
            query = query.where(Student.student_number.like(f'%{query_object.student_number}%'))
        if query_object.class_id:
            query = query.where(Student.class_id == query_object.class_id)
        if query_object.status:
            query = query.where(Student.status == query_object.status)
        if query_object.contact_phone:
            query = query.where(Student.contact_phone.like(f'%{query_object.contact_phone}%'))
        if query_object.begin_time and query_object.end_time:
            query = query.where(
                and_(Student.create_time >= query_object.begin_time, Student.create_time <= query_object.end_time)
            )
        
        query = query.order_by(desc(Student.create_time))
        
        if is_page:
            return await PageUtil.paginate(query_db, query)
        else:
            result = await query_db.execute(query)
            return result.scalars().all()

    @classmethod
    async def get_student_detail_by_id(cls, query_db: AsyncSession, student_id: str):
        """
        根据学生id获取学生详细信息

        :param query_db: orm对象
        :param student_id: 学生id
        :return: 学生信息对象
        """
        query = select(Student).where(Student.id == student_id, Student.is_delete == '0')
        result = await query_db.execute(query)
        return result.scalars().first()

    @classmethod
    async def get_student_detail_by_info(cls, query_db: AsyncSession, student: StudentModel):
        """
        根据学生参数获取学生信息

        :param query_db: orm对象
        :param student: 学生参数对象
        :return: 学生信息对象
        """
        query = select(Student).where(Student.is_delete == '0')
        if student.id:
            query = query.where(Student.id != student.id)
        if student.student_number:
            query = query.where(Student.student_number == student.student_number)
        result = await query_db.execute(query)
        return result.scalars().first()

    @classmethod
    async def add_student_dao(cls, query_db: AsyncSession, student: StudentModel):
        """
        新增学生数据库操作

        :param query_db: orm对象
        :param student: 学生对象
        :return: 新增校验结果
        """
        db_student = Student(**student.model_dump(exclude_unset=True))
        query_db.add(db_student)
        await query_db.flush()
        return db_student

    @classmethod
    async def edit_student_dao(cls, query_db: AsyncSession, student: StudentModel):
        """
        编辑学生数据库操作

        :param query_db: orm对象
        :param student: 学生对象
        :return: 编辑校验结果
        """
        query = (
            update(Student)
            .where(Student.id == student.id)
            .values(**student.model_dump(exclude_unset=True, exclude={'id'}))
        )
        await query_db.execute(query)

    @classmethod
    async def delete_student_dao(cls, query_db: AsyncSession, student: StudentModel):
        """
        删除学生数据库操作

        :param query_db: orm对象
        :param student: 学生对象
        :return: 删除校验结果
        """
        query = (
            update(Student)
            .where(Student.id == student.id)
            .values(is_delete='1', update_time=student.update_time)
        )
        await query_db.execute(query)
